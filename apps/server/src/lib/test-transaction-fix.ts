// Test script to verify the MongoDB ID query fix
// Issue: Categories could not be found by their own ID immediately after creation
// Root cause: <PERSON>hem<PERSON> defined _id as String but MongoDB stored ObjectIds
// Solution: Remove custom _id definitions to use MongoDB's default ObjectId behavior
import "dotenv/config";
import mongoose from "mongoose";

import { User } from "../db/models/auth.model";
import { Category } from "../db/models/financial.model";

// Connect to the myDB database specifically
const databaseUrl = process.env.DATABASE_URL || "";

await mongoose.connect(databaseUrl).catch((error) => {
  console.log("Error connecting to database:", error);
});

async function testTransactionFix() {
  try {
    console.log("Database connection state:", Category.db.readyState);
    console.log("Database name:", Category.db.name);
    console.log("Collection name:", Category.collection.name);

    const user = await User.findOne({ email: "<EMAIL>" });
    console.log("Test user:", user);

    if (!user) {
      console.log("Test user not found");
      return;
    }

    console.log("\n--- Checking all categories in database ---");
    const allCategories = await Category.find({});
    console.log(`Total categories in database: ${allCategories.length}`);

    console.log("\n--- Checking user categories ---");
    const categories = await Category.find({
      userId: user.id,
    });
    console.log(`User categories found: ${categories.length}`);

    const foodCategoryAll = categories.find(
      (cat) => cat.name === "Food & Dining"
    );
    console.log("Food & Dining category:", foodCategoryAll);

    if (foodCategoryAll && foodCategoryAll._id) {
      console.log("ID type:", typeof foodCategoryAll._id);
      console.log("ID value:", foodCategoryAll._id);
      console.log("ID toString():", foodCategoryAll._id.toString());

      // Test different query approaches
      console.log("\n--- Testing different query approaches ---");

      // Let's also check what other categories look like
      console.log("\n--- Checking other categories for comparison ---");
      const otherCategory = categories.find(
        (cat) => cat.name !== "Food & Dining"
      );
      if (otherCategory) {
        console.log("Other category ID type:", typeof otherCategory._id);
        console.log("Other category ID value:", otherCategory._id);

        const otherTest = await Category.findOne({ _id: otherCategory._id });
        console.log(
          "Other category query result:",
          otherTest ? "✅ Found" : "❌ Not found"
        );
      }

      // Let's try a raw MongoDB query to see what's actually stored
      console.log("\n--- Raw MongoDB query ---");
      try {
        const db = Category.db.db;
        if (db) {
          const collection = db.collection("category");

          // First, let's see what documents actually exist
          const allDocs = await collection.find({}).limit(5).toArray();
          console.log("Sample documents in collection:");
          allDocs.forEach((doc, i) => {
            console.log(
              `  ${i + 1}. _id type: ${typeof doc._id}, value: ${
                doc._id
              }, name: ${doc.name}`
            );
          });

          // Try to find by the exact ID we have
          const rawResult = await collection.findOne({
            _id: foodCategoryAll._id,
          });
          console.log(
            "Raw MongoDB query result:",
            rawResult ? "✅ Found" : "❌ Not found"
          );

          // Try to find by ObjectId version
          const mongoose = await import("mongoose");
          try {
            const objectIdResult = await collection.findOne({
              _id: new mongoose.Types.ObjectId(foodCategoryAll._id.toString()),
            });
            console.log(
              "Raw ObjectId query result:",
              objectIdResult ? "✅ Found" : "❌ Not found"
            );
          } catch (e: any) {
            console.log("Raw ObjectId query failed:", e?.message || e);
          }
        }
      } catch (e: any) {
        console.log("Raw query failed:", e?.message || e);
      }

      // Approach 1: Direct ID (current failing approach)
      const test1 = await Category.findOne({
        _id: foodCategoryAll._id.toString(),
      });
      console.log(
        "1. String ID query result:",
        test1 ? "✅ Found" : "❌ Not found"
      );

      // Approach 2: Using the ID directly without toString()
      const test2 = await Category.findOne({
        _id: foodCategoryAll._id,
      });
      console.log(
        "2. Direct ID query result:",
        test2 ? "✅ Found" : "❌ Not found"
      );

      // Approach 3: Using mongoose.Types.ObjectId
      const mongoose = await import("mongoose");
      try {
        const test3 = await Category.findOne({
          _id: new mongoose.Types.ObjectId(foodCategoryAll._id.toString()),
        });
        console.log(
          "3. ObjectId query result:",
          test3 ? "✅ Found" : "❌ Not found"
        );
      } catch (e: any) {
        console.log("3. ObjectId query failed:", e?.message || e);
      }

      // Approach 4: Using findById
      const test4 = await Category.findById(foodCategoryAll._id);
      console.log("4. findById result:", test4 ? "✅ Found" : "❌ Not found");
    }

    // Test the fix: Find Food & Dining category using the corrected schema
    console.log("\n--- Testing the fix ---");
    if (foodCategoryAll && foodCategoryAll._id) {
      // Now that we've fixed the schema, this should work
      const foodCategory = await Category.findById(foodCategoryAll._id);
      console.log(
        "findById result:",
        foodCategory ? "✅ Found" : "❌ Not found"
      );

      // Also test findOne with _id
      const foodCategory2 = await Category.findOne({
        _id: foodCategoryAll._id,
      });
      console.log(
        "findOne with _id result:",
        foodCategory2 ? "✅ Found" : "❌ Not found"
      );

      if (foodCategory) {
        console.log("✅ Category lookup now works!");
        console.log("Category ID:", foodCategory._id);
        console.log("Category ID type:", typeof foodCategory._id);
        console.log("User ID:", foodCategory.userId);
        console.log("Is Default:", foodCategory.isDefault);
      } else {
        console.log("❌ Food & Dining category still not found");
      }
    }
  } catch (error) {
    console.error("Error:", error);
  }
}

testTransactionFix()
  .then(() => {
    process.exit(0);
  })
  .catch((error) => {
    process.exit(1);
  })
  .finally(() => {
    mongoose.disconnect();
  });
