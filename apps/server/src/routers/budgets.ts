import { TRPCError } from "@trpc/server";

import { Budget, Category, Transaction } from "../db/models/financial.model";
import { protectedProcedure, router } from "../lib/trpc";
import {
  createBudgetSchema,
  updateBudgetSchema,
  budgetIdSchema,
} from "../lib/schemas";

export const budgetsRouter = router({
  // Get all budgets for the authenticated user
  getAll: protectedProcedure.query(async ({ ctx }) => {
    try {
      const budgets = await Budget.find({
        userId: ctx.session.user.id,
      })
        .populate("categoryIds", "name type color icon")
        .sort({ createdAt: -1 });

      return budgets;
    } catch (error) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch budgets",
      });
    }
  }),

  // Get active budgets
  getActive: protectedProcedure.query(async ({ ctx }) => {
    try {
      const now = new Date();
      const budgets = await Budget.find({
        userId: ctx.session.user.id,
        isActive: true,
        startDate: { $lte: now },
        endDate: { $gte: now },
      })
        .populate("categoryIds", "name type color icon")
        .sort({ createdAt: -1 });

      return budgets;
    } catch (error) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch active budgets",
      });
    }
  }),

  // Create a new budget
  create: protectedProcedure
    .input(createBudgetSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        // Verify all categories exist and belong to user
        const categories = await Category.find({
          _id: { $in: input.categoryIds },
          $or: [{ userId: ctx.session.user.id }, { isDefault: true }],
        });

        if (categories.length !== input.categoryIds.length) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "One or more categories not found",
          });
        }

        // Check for overlapping budgets with same categories
        const overlappingBudget = await Budget.findOne({
          userId: ctx.session.user.id,
          isActive: true,
          categoryIds: { $in: input.categoryIds },
          $or: [
            {
              startDate: { $lte: input.endDate },
              endDate: { $gte: input.startDate },
            },
          ],
        });

        if (overlappingBudget) {
          throw new TRPCError({
            code: "CONFLICT",
            message:
              "Budget period overlaps with existing budget for one or more categories",
          });
        }

        const budget = new Budget({
          ...input,
          userId: ctx.session.user.id,
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        await budget.save();
        await budget.populate("categoryIds", "name type color icon");

        return budget;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create budget",
        });
      }
    }),

  // Update an existing budget
  update: protectedProcedure
    .input(budgetIdSchema.merge(updateBudgetSchema))
    .mutation(async ({ ctx, input }) => {
      try {
        const { id, ...updateData } = input;

        const budget = await Budget.findOne({
          _id: id,
          userId: ctx.session.user.id,
        });

        if (!budget) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Budget not found",
          });
        }

        // If updating categories, verify they exist
        if (updateData.categoryIds) {
          const categories = await Category.find({
            _id: { $in: updateData.categoryIds },
            $or: [{ userId: ctx.session.user.id }, { isDefault: true }],
          });

          if (categories.length !== updateData.categoryIds.length) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: "One or more categories not found",
            });
          }
        }

        // Check for overlapping budgets if dates or categories are being updated
        if (
          updateData.startDate ||
          updateData.endDate ||
          updateData.categoryIds
        ) {
          const startDate = updateData.startDate || budget.startDate;
          const endDate = updateData.endDate || budget.endDate;
          const categoryIds = updateData.categoryIds || budget.categoryIds;

          const overlappingBudget = await Budget.findOne({
            _id: { $ne: id },
            userId: ctx.session.user.id,
            isActive: true,
            categoryIds: { $in: categoryIds },
            $or: [
              {
                startDate: { $lte: endDate },
                endDate: { $gte: startDate },
              },
            ],
          });

          if (overlappingBudget) {
            throw new TRPCError({
              code: "CONFLICT",
              message:
                "Budget period overlaps with existing budget for one or more categories",
            });
          }
        }

        Object.assign(budget, updateData);
        budget.updatedAt = new Date();

        await budget.save();
        await budget.populate("categoryIds", "name type color icon");

        return budget;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update budget",
        });
      }
    }),

  // Delete a budget
  delete: protectedProcedure
    .input(budgetIdSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        const budget = await Budget.findOne({
          _id: input.id,
          userId: ctx.session.user.id,
        });

        if (!budget) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Budget not found",
          });
        }

        await Budget.deleteOne({ _id: input.id });
        return { success: true };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete budget",
        });
      }
    }),

  // Get a single budget by ID with spending analysis
  getById: protectedProcedure
    .input(budgetIdSchema)
    .query(async ({ ctx, input }) => {
      try {
        const budget = await Budget.findOne({
          _id: input.id,
          userId: ctx.session.user.id,
        }).populate("categoryIds", "name type color icon");

        if (!budget) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Budget not found",
          });
        }

        // Calculate spending for this budget period
        const spending = await Transaction.aggregate([
          {
            $match: {
              userId: ctx.session.user.id,
              categoryId: { $in: budget.categoryIds },
              type: "expense",
              date: {
                $gte: budget.startDate,
                $lte: budget.endDate,
              },
            },
          },
          {
            $group: {
              _id: null,
              totalSpent: { $sum: "$amount" },
              transactionCount: { $sum: 1 },
            },
          },
        ]);

        const totalSpent = spending[0]?.totalSpent || 0;
        const transactionCount = spending[0]?.transactionCount || 0;
        const remaining = budget.amount - totalSpent;
        const percentageUsed =
          budget.amount > 0 ? (totalSpent / budget.amount) * 100 : 0;

        return {
          ...budget.toObject(),
          analysis: {
            totalSpent,
            remaining,
            percentageUsed,
            transactionCount,
            isOverBudget: totalSpent > budget.amount,
            shouldAlert: percentageUsed >= budget.alertThreshold,
          },
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch budget",
        });
      }
    }),

  // Get budget overview for dashboard
  getOverview: protectedProcedure.query(async ({ ctx }) => {
    try {
      const now = new Date();
      const activeBudgets = await Budget.find({
        userId: ctx.session.user.id,
        isActive: true,
        startDate: { $lte: now },
        endDate: { $gte: now },
      }).populate("categoryIds", "name type color icon");

      const budgetAnalysis = await Promise.all(
        activeBudgets.map(async (budget) => {
          const spending = await Transaction.aggregate([
            {
              $match: {
                userId: ctx.session.user.id,
                categoryId: { $in: budget.categoryIds },
                type: "expense",
                date: {
                  $gte: budget.startDate,
                  $lte: budget.endDate,
                },
              },
            },
            {
              $group: {
                _id: null,
                totalSpent: { $sum: "$amount" },
              },
            },
          ]);

          const totalSpent = spending[0]?.totalSpent || 0;
          const percentageUsed =
            budget.amount > 0 ? (totalSpent / budget.amount) * 100 : 0;

          return {
            ...budget.toObject(),
            totalSpent,
            remaining: budget.amount - totalSpent,
            percentageUsed,
            isOverBudget: totalSpent > budget.amount,
            shouldAlert: percentageUsed >= budget.alertThreshold,
          };
        })
      );

      const totalBudgeted = budgetAnalysis.reduce(
        (sum, b) => sum + b.amount,
        0
      );
      const totalSpent = budgetAnalysis.reduce(
        (sum, b) => sum + b.totalSpent,
        0
      );
      const overBudgetCount = budgetAnalysis.filter(
        (b) => b.isOverBudget
      ).length;
      const alertCount = budgetAnalysis.filter((b) => b.shouldAlert).length;

      return {
        budgets: budgetAnalysis,
        summary: {
          totalBudgeted,
          totalSpent,
          totalRemaining: totalBudgeted - totalSpent,
          overBudgetCount,
          alertCount,
          activeBudgetCount: activeBudgets.length,
        },
      };
    } catch (error) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch budget overview",
      });
    }
  }),
});
