import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import {
  Briefcase,
  Laptop,
  TrendingUp,
  Gift,
  Plus,
  UtensilsCrossed,
  Car,
  ShoppingBag,
  Film,
  Receipt,
  Heart,
  GraduationCap,
  Plane,
  Shield,
  PiggyBank,
  Minus,
  DollarSign,
  Home,
  Smartphone,
  Coffee,
  Gamepad2,
  Music,
  Book,
  Dumbbell,
  Palette,
} from "lucide-react";

import { trpc } from "@/utils/trpc";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";

// Category form schema
const categoryFormSchema = z.object({
  name: z
    .string()
    .min(1, "Category name is required")
    .max(50, "Category name too long"),
  type: z.enum(["income", "expense"]),
  color: z
    .string()
    .regex(/^#[0-9A-F]{6}$/i, "Invalid color format"),
  icon: z.string().min(1, "Icon is required"),
});

type CategoryFormValues = z.infer<typeof categoryFormSchema>;

// Available icons for categories
const availableIcons = [
  { name: "DollarSign", icon: DollarSign, label: "Dollar Sign" },
  { name: "Briefcase", icon: Briefcase, label: "Briefcase" },
  { name: "Laptop", icon: Laptop, label: "Laptop" },
  { name: "TrendingUp", icon: TrendingUp, label: "Trending Up" },
  { name: "Gift", icon: Gift, label: "Gift" },
  { name: "Plus", icon: Plus, label: "Plus" },
  { name: "UtensilsCrossed", icon: UtensilsCrossed, label: "Food" },
  { name: "Car", icon: Car, label: "Car" },
  { name: "ShoppingBag", icon: ShoppingBag, label: "Shopping" },
  { name: "Film", icon: Film, label: "Entertainment" },
  { name: "Receipt", icon: Receipt, label: "Receipt" },
  { name: "Heart", icon: Heart, label: "Health" },
  { name: "GraduationCap", icon: GraduationCap, label: "Education" },
  { name: "Plane", icon: Plane, label: "Travel" },
  { name: "Shield", icon: Shield, label: "Insurance" },
  { name: "PiggyBank", icon: PiggyBank, label: "Savings" },
  { name: "Minus", icon: Minus, label: "Minus" },
  { name: "Home", icon: Home, label: "Home" },
  { name: "Smartphone", icon: Smartphone, label: "Phone" },
  { name: "Coffee", icon: Coffee, label: "Coffee" },
  { name: "Gamepad2", icon: Gamepad2, label: "Gaming" },
  { name: "Music", icon: Music, label: "Music" },
  { name: "Book", icon: Book, label: "Books" },
  { name: "Dumbbell", icon: Dumbbell, label: "Fitness" },
  { name: "Palette", icon: Palette, label: "Art" },
];

// Predefined color options
const colorOptions = [
  "#EF4444", // Red
  "#F97316", // Orange
  "#F59E0B", // Amber
  "#EAB308", // Yellow
  "#84CC16", // Lime
  "#22C55E", // Green
  "#10B981", // Emerald
  "#06B6D4", // Cyan
  "#0EA5E9", // Sky
  "#3B82F6", // Blue
  "#6366F1", // Indigo
  "#8B5CF6", // Violet
  "#A855F7", // Purple
  "#D946EF", // Fuchsia
  "#EC4899", // Pink
  "#F43F5E", // Rose
  "#6B7280", // Gray
  "#374151", // Gray Dark
];

interface CategoryFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  category?: any;
}

export function CategoryForm({ open, onOpenChange, category }: CategoryFormProps) {
  const queryClient = useQueryClient();
  const [selectedIcon, setSelectedIcon] = useState<string>("DollarSign");
  const [selectedColor, setSelectedColor] = useState<string>("#3B82F6");

  const form = useForm<CategoryFormValues>({
    resolver: zodResolver(categoryFormSchema),
    defaultValues: {
      name: "",
      type: "expense",
      color: "#3B82F6",
      icon: "DollarSign",
    },
  });

  // Create category mutation
  const createCategory = useMutation(
    trpc.categories.create.mutationOptions({
      onSuccess: () => {
        toast.success("Category created successfully!");
        queryClient.invalidateQueries(trpc.categories.getAll.queryOptions());
        onOpenChange(false);
        form.reset();
      },
      onError: (error: any) => {
        toast.error(error.message || "Failed to create category");
      },
    })
  );

  // Update category mutation
  const updateCategory = useMutation(
    trpc.categories.update.mutationOptions({
      onSuccess: () => {
        toast.success("Category updated successfully!");
        queryClient.invalidateQueries(trpc.categories.getAll.queryOptions());
        onOpenChange(false);
        form.reset();
      },
      onError: (error: any) => {
        toast.error(error.message || "Failed to update category");
      },
    })
  );

  // Reset form when dialog opens/closes or category changes
  useEffect(() => {
    if (open) {
      if (category) {
        // Editing existing category
        form.reset({
          name: category.name,
          type: category.type,
          color: category.color,
          icon: category.icon,
        });
        setSelectedIcon(category.icon);
        setSelectedColor(category.color);
      } else {
        // Creating new category
        form.reset({
          name: "",
          type: "expense",
          color: "#3B82F6",
          icon: "DollarSign",
        });
        setSelectedIcon("DollarSign");
        setSelectedColor("#3B82F6");
      }
    }
  }, [open, category, form]);

  const onSubmit = (values: CategoryFormValues) => {
    if (category) {
      // Update existing category
      updateCategory.mutate({
        id: category._id,
        ...values,
      });
    } else {
      // Create new category
      createCategory.mutate(values);
    }
  };

  const handleIconSelect = (iconName: string) => {
    setSelectedIcon(iconName);
    form.setValue("icon", iconName);
  };

  const handleColorSelect = (color: string) => {
    setSelectedColor(color);
    form.setValue("color", color);
  };

  const isLoading = createCategory.isPending || updateCategory.isPending;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {category ? "Edit Category" : "Create New Category"}
          </DialogTitle>
          <DialogDescription>
            {category
              ? "Update the category details below."
              : "Add a new category to organize your transactions."}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Category Name */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Category Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter category name"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Category Type */}
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Type</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={isLoading}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="income">Income</SelectItem>
                      <SelectItem value="expense">Expense</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Icon Selection */}
            <FormField
              control={form.control}
              name="icon"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Icon</FormLabel>
                  <FormDescription>
                    Choose an icon to represent this category
                  </FormDescription>
                  <div className="grid grid-cols-6 gap-2 max-h-32 overflow-y-auto border rounded-md p-2">
                    {availableIcons.map((iconOption) => {
                      const IconComponent = iconOption.icon;
                      return (
                        <button
                          key={iconOption.name}
                          type="button"
                          onClick={() => handleIconSelect(iconOption.name)}
                          className={`p-2 rounded-md border-2 transition-colors ${
                            selectedIcon === iconOption.name
                              ? "border-primary bg-primary/10"
                              : "border-border hover:border-primary/50"
                          }`}
                          disabled={isLoading}
                        >
                          <IconComponent className="h-4 w-4 mx-auto" />
                        </button>
                      );
                    })}
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">Selected:</span>
                    <Badge variant="outline" className="gap-1">
                      {(() => {
                        const SelectedIcon = availableIcons.find(
                          (icon) => icon.name === selectedIcon
                        )?.icon;
                        return SelectedIcon ? <SelectedIcon className="h-3 w-3" /> : null;
                      })()}
                      {selectedIcon}
                    </Badge>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Color Selection */}
            <FormField
              control={form.control}
              name="color"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Color</FormLabel>
                  <FormDescription>
                    Choose a color to represent this category
                  </FormDescription>
                  <div className="grid grid-cols-9 gap-2">
                    {colorOptions.map((color) => (
                      <button
                        key={color}
                        type="button"
                        onClick={() => handleColorSelect(color)}
                        className={`w-8 h-8 rounded-md border-2 transition-all ${
                          selectedColor === color
                            ? "border-primary scale-110"
                            : "border-border hover:scale-105"
                        }`}
                        style={{ backgroundColor: color }}
                        disabled={isLoading}
                        title={color}
                      />
                    ))}
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">Selected:</span>
                    <div className="flex items-center gap-2">
                      <div
                        className="w-4 h-4 rounded-full border"
                        style={{ backgroundColor: selectedColor }}
                      />
                      <span className="text-sm font-mono">{selectedColor}</span>
                    </div>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading
                  ? category
                    ? "Updating..."
                    : "Creating..."
                  : category
                  ? "Update Category"
                  : "Create Category"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
