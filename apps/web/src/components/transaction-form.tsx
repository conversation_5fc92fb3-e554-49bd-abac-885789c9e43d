import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { trpc } from "@/utils/trpc";
import { toast } from "sonner";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { CalendarIcon, Loader2 } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

// Form schema
const transactionFormSchema = z.object({
  amount: z
    .string()
    .min(1, "Amount is required")
    .refine((val) => !isNaN(Number(val)) && Number(val) > 0, {
      message: "Amount must be a positive number",
    }),
  type: z.enum(["income", "expense"]),
  description: z
    .string()
    .min(1, "Description is required")
    .max(200, "Description too long"),
  categoryId: z.string().min(1, "Please select a category"),
  date: z.date({
    error: "Please select a date",
  }),
  tags: z.string().optional(),
});

type TransactionFormValues = z.infer<typeof transactionFormSchema>;

interface TransactionFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  defaultType?: "income" | "expense";
  transaction?: any; // For editing existing transactions
}

export function TransactionForm({
  open,
  onOpenChange,
  defaultType = "expense",
  transaction,
}: TransactionFormProps) {
  const queryClient = useQueryClient();
  const [selectedType, setSelectedType] = useState<"income" | "expense">(
    transaction?.type || defaultType
  );

  // Fetch categories based on selected type
  const { data: categories, isLoading: categoriesLoading } = useQuery(
    trpc.categories.getByType.queryOptions({
      type: selectedType,
    })
  );

  // Form setup
  const form = useForm<TransactionFormValues>({
    resolver: zodResolver(transactionFormSchema),
    defaultValues: {
      amount: transaction?.amount?.toString() || "",
      type: transaction?.type || defaultType,
      description: transaction?.description || "",
      categoryId: transaction?.categoryId?._id || "",
      date: transaction?.date ? new Date(transaction.date) : new Date(),
      tags: transaction?.tags?.join(", ") || "",
    },
  });

  // Mutations using tRPC
  const createTransactionMutation = useMutation(
    trpc.transactions.create.mutationOptions({
      onSettled: () => {},
      onSuccess: () => {
        toast.success("Transaction created successfully!");
        queryClient.invalidateQueries(
          trpc.transactions.getAll.queryOptions({
            page: 1,
            limit: 20,
          })
        );
        queryClient.invalidateQueries(
          trpc.transactions.getSummary.queryOptions({
            startDate: new Date(),
            endDate: new Date(),
          })
        );
        onOpenChange(false);
        form.reset();
      },
      onError: (error: any) => {
        toast.error(error.message || "Failed to create transaction");
      },
    })
  );

  const updateTransactionMutation = useMutation(
    trpc.transactions.update.mutationOptions({
      onSettled: () => {},
      onSuccess: () => {
        toast.success("Transaction updated successfully!");
        queryClient.invalidateQueries(
          trpc.transactions.getAll.queryOptions({
            page: 1,
            limit: 20,
          })
        );
        queryClient.invalidateQueries(
          trpc.transactions.getSummary.queryOptions({
            startDate: new Date(),
            endDate: new Date(),
          })
        );
        onOpenChange(false);
      },
      onError: (error: any) => {
        toast.error(error.message || "Failed to update transaction");
      },
    })
  );

  const onSubmit = (values: TransactionFormValues) => {
    console.log(values);

    const transactionData = {
      amount: Number(values.amount),
      type: values.type,
      description: values.description,
      categoryId: values.categoryId,
      date: values.date,
      tags: values.tags
        ? values.tags
            .split(",")
            .map((tag) => tag.trim())
            .filter(Boolean)
        : undefined,
    };

    if (transaction) {
      updateTransactionMutation.mutate({
        id: transaction._id,
        ...transactionData,
      });
    } else {
      createTransactionMutation.mutate(transactionData);
    }
  };

  const handleTypeChange = (type: "income" | "expense") => {
    setSelectedType(type);
    form.setValue("type", type);
    form.setValue("categoryId", ""); // Reset category when type changes
  };

  const isLoading =
    createTransactionMutation.isPending || updateTransactionMutation.isPending;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {transaction ? "Edit Transaction" : "Add New Transaction"}
          </DialogTitle>
          <DialogDescription>
            {transaction
              ? "Update the transaction details below."
              : "Enter the details for your new transaction."}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Transaction Type */}
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Type</FormLabel>
                  <Select
                    onValueChange={handleTypeChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select transaction type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="income">Income</SelectItem>
                      <SelectItem value="expense">Expense</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Amount */}
            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Amount</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                        $
                      </span>
                      <Input
                        placeholder="0.00"
                        className="pl-8"
                        type="number"
                        step="0.01"
                        min="0"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Category */}
            <FormField
              control={form.control}
              name="categoryId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Category</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={categoriesLoading}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a category" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {categories?.map((category: any) => (
                        <SelectItem key={category._id} value={category._id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Categories are filtered by transaction type.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter transaction description..."
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Date */}
            <FormField
              control={form.control}
              name="date"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Date</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) =>
                          date > new Date() || date < new Date("1900-01-01")
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Tags */}
            <FormField
              control={form.control}
              name="tags"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tags (Optional)</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., groceries, utilities, salary"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Separate multiple tags with commas.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {transaction ? "Update" : "Create"} Transaction
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
