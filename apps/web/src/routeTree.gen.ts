/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as SidebarRouteRouteImport } from './routes/_sidebar.route'
import { Route as AuthRouteRouteImport } from './routes/_auth.route'
import { Route as IndexRouteImport } from './routes/index'
import { Route as SidebarTransactionsIndexRouteImport } from './routes/_sidebar.transactions.index'
import { Route as SidebarDashboardIndexRouteImport } from './routes/_sidebar.dashboard.index'
import { Route as SidebarCategoriesIndexRouteImport } from './routes/_sidebar.categories.index'
import { Route as AuthRegisterIndexRouteImport } from './routes/_auth.register.index'
import { Route as AuthLoginIndexRouteImport } from './routes/_auth.login.index'

const SidebarRouteRoute = SidebarRouteRouteImport.update({
  id: '/_sidebar',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthRouteRoute = AuthRouteRouteImport.update({
  id: '/_auth',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const SidebarTransactionsIndexRoute =
  SidebarTransactionsIndexRouteImport.update({
    id: '/transactions/',
    path: '/transactions/',
    getParentRoute: () => SidebarRouteRoute,
  } as any)
const SidebarDashboardIndexRoute = SidebarDashboardIndexRouteImport.update({
  id: '/dashboard/',
  path: '/dashboard/',
  getParentRoute: () => SidebarRouteRoute,
} as any)
const SidebarCategoriesIndexRoute = SidebarCategoriesIndexRouteImport.update({
  id: '/categories/',
  path: '/categories/',
  getParentRoute: () => SidebarRouteRoute,
} as any)
const AuthRegisterIndexRoute = AuthRegisterIndexRouteImport.update({
  id: '/register/',
  path: '/register/',
  getParentRoute: () => AuthRouteRoute,
} as any)
const AuthLoginIndexRoute = AuthLoginIndexRouteImport.update({
  id: '/login/',
  path: '/login/',
  getParentRoute: () => AuthRouteRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/login': typeof AuthLoginIndexRoute
  '/register': typeof AuthRegisterIndexRoute
  '/categories': typeof SidebarCategoriesIndexRoute
  '/dashboard': typeof SidebarDashboardIndexRoute
  '/transactions': typeof SidebarTransactionsIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/login': typeof AuthLoginIndexRoute
  '/register': typeof AuthRegisterIndexRoute
  '/categories': typeof SidebarCategoriesIndexRoute
  '/dashboard': typeof SidebarDashboardIndexRoute
  '/transactions': typeof SidebarTransactionsIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/_auth': typeof AuthRouteRouteWithChildren
  '/_sidebar': typeof SidebarRouteRouteWithChildren
  '/_auth/login/': typeof AuthLoginIndexRoute
  '/_auth/register/': typeof AuthRegisterIndexRoute
  '/_sidebar/categories/': typeof SidebarCategoriesIndexRoute
  '/_sidebar/dashboard/': typeof SidebarDashboardIndexRoute
  '/_sidebar/transactions/': typeof SidebarTransactionsIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/login'
    | '/register'
    | '/categories'
    | '/dashboard'
    | '/transactions'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/login'
    | '/register'
    | '/categories'
    | '/dashboard'
    | '/transactions'
  id:
    | '__root__'
    | '/'
    | '/_auth'
    | '/_sidebar'
    | '/_auth/login/'
    | '/_auth/register/'
    | '/_sidebar/categories/'
    | '/_sidebar/dashboard/'
    | '/_sidebar/transactions/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AuthRouteRoute: typeof AuthRouteRouteWithChildren
  SidebarRouteRoute: typeof SidebarRouteRouteWithChildren
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_sidebar': {
      id: '/_sidebar'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof SidebarRouteRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_auth': {
      id: '/_auth'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthRouteRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_sidebar/transactions/': {
      id: '/_sidebar/transactions/'
      path: '/transactions'
      fullPath: '/transactions'
      preLoaderRoute: typeof SidebarTransactionsIndexRouteImport
      parentRoute: typeof SidebarRouteRoute
    }
    '/_sidebar/dashboard/': {
      id: '/_sidebar/dashboard/'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof SidebarDashboardIndexRouteImport
      parentRoute: typeof SidebarRouteRoute
    }
    '/_sidebar/categories/': {
      id: '/_sidebar/categories/'
      path: '/categories'
      fullPath: '/categories'
      preLoaderRoute: typeof SidebarCategoriesIndexRouteImport
      parentRoute: typeof SidebarRouteRoute
    }
    '/_auth/register/': {
      id: '/_auth/register/'
      path: '/register'
      fullPath: '/register'
      preLoaderRoute: typeof AuthRegisterIndexRouteImport
      parentRoute: typeof AuthRouteRoute
    }
    '/_auth/login/': {
      id: '/_auth/login/'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof AuthLoginIndexRouteImport
      parentRoute: typeof AuthRouteRoute
    }
  }
}

interface AuthRouteRouteChildren {
  AuthLoginIndexRoute: typeof AuthLoginIndexRoute
  AuthRegisterIndexRoute: typeof AuthRegisterIndexRoute
}

const AuthRouteRouteChildren: AuthRouteRouteChildren = {
  AuthLoginIndexRoute: AuthLoginIndexRoute,
  AuthRegisterIndexRoute: AuthRegisterIndexRoute,
}

const AuthRouteRouteWithChildren = AuthRouteRoute._addFileChildren(
  AuthRouteRouteChildren,
)

interface SidebarRouteRouteChildren {
  SidebarCategoriesIndexRoute: typeof SidebarCategoriesIndexRoute
  SidebarDashboardIndexRoute: typeof SidebarDashboardIndexRoute
  SidebarTransactionsIndexRoute: typeof SidebarTransactionsIndexRoute
}

const SidebarRouteRouteChildren: SidebarRouteRouteChildren = {
  SidebarCategoriesIndexRoute: SidebarCategoriesIndexRoute,
  SidebarDashboardIndexRoute: SidebarDashboardIndexRoute,
  SidebarTransactionsIndexRoute: SidebarTransactionsIndexRoute,
}

const SidebarRouteRouteWithChildren = SidebarRouteRoute._addFileChildren(
  SidebarRouteRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AuthRouteRoute: AuthRouteRouteWithChildren,
  SidebarRouteRoute: SidebarRouteRouteWithChildren,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
